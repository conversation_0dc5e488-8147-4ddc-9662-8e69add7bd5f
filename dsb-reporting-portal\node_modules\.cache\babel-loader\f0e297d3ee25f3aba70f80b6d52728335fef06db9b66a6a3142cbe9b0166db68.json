{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\DSB-ReportingPortal\\\\dsb-reporting-portal\\\\src\\\\pages\\\\SalesReports.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Grid, Paper, Card, CardContent, FormControl, InputLabel, Select, MenuItem, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip } from '@mui/material';\n// Date picker imports removed for simplicity\nimport { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, LineChart, Line, PieChart, Pie, Cell, ResponsiveContainer } from 'recharts';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst salesData = [{\n  month: 'Jan',\n  sales: 65000,\n  target: 70000,\n  growth: 12\n}, {\n  month: 'Feb',\n  sales: 72000,\n  target: 75000,\n  growth: 15\n}, {\n  month: 'Mar',\n  sales: 68000,\n  target: 70000,\n  growth: 8\n}, {\n  month: 'Apr',\n  sales: 81000,\n  target: 80000,\n  growth: 22\n}, {\n  month: 'May',\n  sales: 79000,\n  target: 85000,\n  growth: 18\n}, {\n  month: 'Jun',\n  sales: 92000,\n  target: 90000,\n  growth: 25\n}];\nconst productSales = [{\n  name: 'Product A',\n  value: 35,\n  color: '#0088FE'\n}, {\n  name: 'Product B',\n  value: 25,\n  color: '#00C49F'\n}, {\n  name: 'Product C',\n  value: 20,\n  color: '#FFBB28'\n}, {\n  name: 'Product D',\n  value: 20,\n  color: '#FF8042'\n}];\nconst topSalesReps = [{\n  name: 'John Smith',\n  sales: 125000,\n  target: 120000,\n  achievement: 104.2\n}, {\n  name: 'Sarah Johnson',\n  sales: 118000,\n  target: 115000,\n  achievement: 102.6\n}, {\n  name: 'Mike Davis',\n  sales: 112000,\n  target: 110000,\n  achievement: 101.8\n}, {\n  name: 'Lisa Wilson',\n  sales: 108000,\n  target: 105000,\n  achievement: 102.9\n}, {\n  name: 'Tom Brown',\n  sales: 95000,\n  target: 100000,\n  achievement: 95.0\n}];\nfunction SalesReports() {\n  _s();\n  const [dateRange, setDateRange] = useState('6months');\n  const [region, setRegion] = useState('all');\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Sales Reports\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      color: \"textSecondary\",\n      paragraph: true,\n      children: \"Comprehensive sales analytics and performance metrics\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Report Filters\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Date Range\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: dateRange,\n              label: \"Date Range\",\n              onChange: e => setDateRange(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"1month\",\n                children: \"Last Month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"3months\",\n                children: \"Last 3 Months\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"6months\",\n                children: \"Last 6 Months\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"1year\",\n                children: \"Last Year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"custom\",\n                children: \"Custom Range\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Region\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: region,\n              label: \"Region\",\n              onChange: e => setRegion(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"all\",\n                children: \"All Regions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"north\",\n                children: \"North\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"south\",\n                children: \"South\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"east\",\n                children: \"East\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"west\",\n                children: \"West\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 13\n        }, this), dateRange === 'custom' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              children: \"Custom date range selection will be available soon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 17\n          }, this)\n        }, void 0, false), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            fullWidth: true,\n            sx: {\n              height: 56\n            },\n            children: \"Generate Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Sales vs Target\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              data: salesData,\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"sales\",\n                fill: \"#1976d2\",\n                name: \"Actual Sales\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"target\",\n                fill: \"#dc004e\",\n                name: \"Target\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Product Sales Distribution\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/_jsxDEV(PieChart, {\n              children: [/*#__PURE__*/_jsxDEV(Pie, {\n                data: productSales,\n                cx: \"50%\",\n                cy: \"50%\",\n                labelLine: false,\n                label: ({\n                  name,\n                  percent\n                }) => `${name} ${(percent * 100).toFixed(0)}%`,\n                outerRadius: 80,\n                fill: \"#8884d8\",\n                dataKey: \"value\",\n                children: productSales.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                  fill: entry.color\n                }, `cell-${index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Sales Growth Trend\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/_jsxDEV(LineChart, {\n              data: salesData,\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"growth\",\n                stroke: \"#00C49F\",\n                strokeWidth: 3,\n                name: \"Growth %\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Top Sales Representatives\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"Sales ($)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"Target ($)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"Achievement (%)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: topSalesReps.map(rep => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                component: \"th\",\n                scope: \"row\",\n                children: rep.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: rep.sales.toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: rep.target.toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: [rep.achievement, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: rep.achievement >= 100 ? 'Target Met' : 'Below Target',\n                  color: rep.achievement >= 100 ? 'success' : 'warning',\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 21\n              }, this)]\n            }, rep.name, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 7\n  }, this);\n}\n_s(SalesReports, \"BSslMwsVEmHWCu0cPy+0CcjeKo8=\");\n_c = SalesReports;\nexport default SalesReports;\nvar _c;\n$RefreshReg$(_c, \"SalesReports\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Grid", "Paper", "Card", "<PERSON><PERSON><PERSON><PERSON>", "FormControl", "InputLabel", "Select", "MenuItem", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Chip", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "Line<PERSON>hart", "Line", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "ResponsiveContainer", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "salesData", "month", "sales", "target", "growth", "productSales", "name", "value", "color", "topSalesReps", "achievement", "SalesReports", "_s", "date<PERSON><PERSON><PERSON>", "setDateRange", "region", "setRegion", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "paragraph", "sx", "p", "mb", "container", "spacing", "item", "xs", "sm", "md", "fullWidth", "label", "onChange", "e", "height", "lg", "width", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "fill", "cx", "cy", "labelLine", "percent", "toFixed", "outerRadius", "map", "entry", "index", "type", "stroke", "strokeWidth", "align", "rep", "component", "scope", "toLocaleString", "size", "_c", "$RefreshReg$"], "sources": ["D:/Augment-projects/DSB-ReportingPortal/dsb-reporting-portal/src/pages/SalesReports.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Grid,\n  Paper,\n  Card,\n  CardContent,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Button,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Chip,\n} from '@mui/material';\n// Date picker imports removed for simplicity\nimport {\n  BarChart,\n  Bar,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  Legend,\n  LineChart,\n  Line,\n  PieChart,\n  Pie,\n  Cell,\n  ResponsiveContainer,\n} from 'recharts';\n\nconst salesData = [\n  { month: 'Jan', sales: 65000, target: 70000, growth: 12 },\n  { month: 'Feb', sales: 72000, target: 75000, growth: 15 },\n  { month: 'Mar', sales: 68000, target: 70000, growth: 8 },\n  { month: 'Apr', sales: 81000, target: 80000, growth: 22 },\n  { month: 'May', sales: 79000, target: 85000, growth: 18 },\n  { month: 'Jun', sales: 92000, target: 90000, growth: 25 },\n];\n\nconst productSales = [\n  { name: 'Product A', value: 35, color: '#0088FE' },\n  { name: 'Product B', value: 25, color: '#00C49F' },\n  { name: 'Product C', value: 20, color: '#FFBB28' },\n  { name: 'Product D', value: 20, color: '#FF8042' },\n];\n\nconst topSalesReps = [\n  { name: 'John Smith', sales: 125000, target: 120000, achievement: 104.2 },\n  { name: 'Sarah Johnson', sales: 118000, target: 115000, achievement: 102.6 },\n  { name: '<PERSON> <PERSON>', sales: 112000, target: 110000, achievement: 101.8 },\n  { name: 'Lisa Wilson', sales: 108000, target: 105000, achievement: 102.9 },\n  { name: 'Tom Brown', sales: 95000, target: 100000, achievement: 95.0 },\n];\n\nfunction SalesReports() {\n  const [dateRange, setDateRange] = useState('6months');\n  const [region, setRegion] = useState('all');\n\n  return (\n      <Box>\n        <Typography variant=\"h4\" gutterBottom>\n          Sales Reports\n        </Typography>\n        <Typography variant=\"body1\" color=\"textSecondary\" paragraph>\n          Comprehensive sales analytics and performance metrics\n        </Typography>\n\n        {/* Filters */}\n        <Paper sx={{ p: 3, mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Report Filters\n          </Typography>\n          <Grid container spacing={3}>\n            <Grid item xs={12} sm={6} md={3}>\n              <FormControl fullWidth>\n                <InputLabel>Date Range</InputLabel>\n                <Select\n                  value={dateRange}\n                  label=\"Date Range\"\n                  onChange={(e) => setDateRange(e.target.value)}\n                >\n                  <MenuItem value=\"1month\">Last Month</MenuItem>\n                  <MenuItem value=\"3months\">Last 3 Months</MenuItem>\n                  <MenuItem value=\"6months\">Last 6 Months</MenuItem>\n                  <MenuItem value=\"1year\">Last Year</MenuItem>\n                  <MenuItem value=\"custom\">Custom Range</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n            <Grid item xs={12} sm={6} md={3}>\n              <FormControl fullWidth>\n                <InputLabel>Region</InputLabel>\n                <Select\n                  value={region}\n                  label=\"Region\"\n                  onChange={(e) => setRegion(e.target.value)}\n                >\n                  <MenuItem value=\"all\">All Regions</MenuItem>\n                  <MenuItem value=\"north\">North</MenuItem>\n                  <MenuItem value=\"south\">South</MenuItem>\n                  <MenuItem value=\"east\">East</MenuItem>\n                  <MenuItem value=\"west\">West</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n            {dateRange === 'custom' && (\n              <>\n                <Grid item xs={12} sm={6} md={3}>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    Custom date range selection will be available soon\n                  </Typography>\n                </Grid>\n              </>\n            )}\n            <Grid item xs={12} sm={6} md={3}>\n              <Button variant=\"contained\" fullWidth sx={{ height: 56 }}>\n                Generate Report\n              </Button>\n            </Grid>\n          </Grid>\n        </Paper>\n\n        {/* Charts */}\n        <Grid container spacing={3} sx={{ mb: 3 }}>\n          <Grid item xs={12} lg={8}>\n            <Paper sx={{ p: 3 }}>\n              <Typography variant=\"h6\" gutterBottom>\n                Sales vs Target\n              </Typography>\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <BarChart data={salesData}>\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"month\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Legend />\n                  <Bar dataKey=\"sales\" fill=\"#1976d2\" name=\"Actual Sales\" />\n                  <Bar dataKey=\"target\" fill=\"#dc004e\" name=\"Target\" />\n                </BarChart>\n              </ResponsiveContainer>\n            </Paper>\n          </Grid>\n          <Grid item xs={12} lg={4}>\n            <Paper sx={{ p: 3 }}>\n              <Typography variant=\"h6\" gutterBottom>\n                Product Sales Distribution\n              </Typography>\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <PieChart>\n                  <Pie\n                    data={productSales}\n                    cx=\"50%\"\n                    cy=\"50%\"\n                    labelLine={false}\n                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\n                    outerRadius={80}\n                    fill=\"#8884d8\"\n                    dataKey=\"value\"\n                  >\n                    {productSales.map((entry, index) => (\n                      <Cell key={`cell-${index}`} fill={entry.color} />\n                    ))}\n                  </Pie>\n                  <Tooltip />\n                </PieChart>\n              </ResponsiveContainer>\n            </Paper>\n          </Grid>\n        </Grid>\n\n        {/* Sales Growth Trend */}\n        <Grid container spacing={3} sx={{ mb: 3 }}>\n          <Grid item xs={12}>\n            <Paper sx={{ p: 3 }}>\n              <Typography variant=\"h6\" gutterBottom>\n                Sales Growth Trend\n              </Typography>\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <LineChart data={salesData}>\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"month\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Legend />\n                  <Line type=\"monotone\" dataKey=\"growth\" stroke=\"#00C49F\" strokeWidth={3} name=\"Growth %\" />\n                </LineChart>\n              </ResponsiveContainer>\n            </Paper>\n          </Grid>\n        </Grid>\n\n        {/* Top Sales Representatives */}\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Top Sales Representatives\n          </Typography>\n          <TableContainer>\n            <Table>\n              <TableHead>\n                <TableRow>\n                  <TableCell>Name</TableCell>\n                  <TableCell align=\"right\">Sales ($)</TableCell>\n                  <TableCell align=\"right\">Target ($)</TableCell>\n                  <TableCell align=\"right\">Achievement (%)</TableCell>\n                  <TableCell align=\"center\">Status</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {topSalesReps.map((rep) => (\n                  <TableRow key={rep.name}>\n                    <TableCell component=\"th\" scope=\"row\">\n                      {rep.name}\n                    </TableCell>\n                    <TableCell align=\"right\">{rep.sales.toLocaleString()}</TableCell>\n                    <TableCell align=\"right\">{rep.target.toLocaleString()}</TableCell>\n                    <TableCell align=\"right\">{rep.achievement}%</TableCell>\n                    <TableCell align=\"center\">\n                      <Chip\n                        label={rep.achievement >= 100 ? 'Target Met' : 'Below Target'}\n                        color={rep.achievement >= 100 ? 'success' : 'warning'}\n                        size=\"small\"\n                      />\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </Paper>\n      </Box>\n  );\n}\n\nexport default SalesReports;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,QACC,eAAe;AACtB;AACA,SACEC,QAAQ,EACRC,GAAG,EACHC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,SAAS,EACTC,IAAI,EACJC,QAAQ,EACRC,GAAG,EACHC,IAAI,EACJC,mBAAmB,QACd,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElB,MAAMC,SAAS,GAAG,CAChB;EAAEC,KAAK,EAAE,KAAK;EAAEC,KAAK,EAAE,KAAK;EAAEC,MAAM,EAAE,KAAK;EAAEC,MAAM,EAAE;AAAG,CAAC,EACzD;EAAEH,KAAK,EAAE,KAAK;EAAEC,KAAK,EAAE,KAAK;EAAEC,MAAM,EAAE,KAAK;EAAEC,MAAM,EAAE;AAAG,CAAC,EACzD;EAAEH,KAAK,EAAE,KAAK;EAAEC,KAAK,EAAE,KAAK;EAAEC,MAAM,EAAE,KAAK;EAAEC,MAAM,EAAE;AAAE,CAAC,EACxD;EAAEH,KAAK,EAAE,KAAK;EAAEC,KAAK,EAAE,KAAK;EAAEC,MAAM,EAAE,KAAK;EAAEC,MAAM,EAAE;AAAG,CAAC,EACzD;EAAEH,KAAK,EAAE,KAAK;EAAEC,KAAK,EAAE,KAAK;EAAEC,MAAM,EAAE,KAAK;EAAEC,MAAM,EAAE;AAAG,CAAC,EACzD;EAAEH,KAAK,EAAE,KAAK;EAAEC,KAAK,EAAE,KAAK;EAAEC,MAAM,EAAE,KAAK;EAAEC,MAAM,EAAE;AAAG,CAAC,CAC1D;AAED,MAAMC,YAAY,GAAG,CACnB;EAAEC,IAAI,EAAE,WAAW;EAAEC,KAAK,EAAE,EAAE;EAAEC,KAAK,EAAE;AAAU,CAAC,EAClD;EAAEF,IAAI,EAAE,WAAW;EAAEC,KAAK,EAAE,EAAE;EAAEC,KAAK,EAAE;AAAU,CAAC,EAClD;EAAEF,IAAI,EAAE,WAAW;EAAEC,KAAK,EAAE,EAAE;EAAEC,KAAK,EAAE;AAAU,CAAC,EAClD;EAAEF,IAAI,EAAE,WAAW;EAAEC,KAAK,EAAE,EAAE;EAAEC,KAAK,EAAE;AAAU,CAAC,CACnD;AAED,MAAMC,YAAY,GAAG,CACnB;EAAEH,IAAI,EAAE,YAAY;EAAEJ,KAAK,EAAE,MAAM;EAAEC,MAAM,EAAE,MAAM;EAAEO,WAAW,EAAE;AAAM,CAAC,EACzE;EAAEJ,IAAI,EAAE,eAAe;EAAEJ,KAAK,EAAE,MAAM;EAAEC,MAAM,EAAE,MAAM;EAAEO,WAAW,EAAE;AAAM,CAAC,EAC5E;EAAEJ,IAAI,EAAE,YAAY;EAAEJ,KAAK,EAAE,MAAM;EAAEC,MAAM,EAAE,MAAM;EAAEO,WAAW,EAAE;AAAM,CAAC,EACzE;EAAEJ,IAAI,EAAE,aAAa;EAAEJ,KAAK,EAAE,MAAM;EAAEC,MAAM,EAAE,MAAM;EAAEO,WAAW,EAAE;AAAM,CAAC,EAC1E;EAAEJ,IAAI,EAAE,WAAW;EAAEJ,KAAK,EAAE,KAAK;EAAEC,MAAM,EAAE,MAAM;EAAEO,WAAW,EAAE;AAAK,CAAC,CACvE;AAED,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACmD,MAAM,EAAEC,SAAS,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAE3C,oBACIiC,OAAA,CAAChC,GAAG;IAAAoD,QAAA,gBACFpB,OAAA,CAAC/B,UAAU;MAACoD,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACb1B,OAAA,CAAC/B,UAAU;MAACoD,OAAO,EAAC,OAAO;MAACV,KAAK,EAAC,eAAe;MAACgB,SAAS;MAAAP,QAAA,EAAC;IAE5D;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGb1B,OAAA,CAAC7B,KAAK;MAACyD,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAV,QAAA,gBACzBpB,OAAA,CAAC/B,UAAU;QAACoD,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb1B,OAAA,CAAC9B,IAAI;QAAC6D,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAZ,QAAA,gBACzBpB,OAAA,CAAC9B,IAAI;UAAC+D,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eAC9BpB,OAAA,CAAC1B,WAAW;YAAC+D,SAAS;YAAAjB,QAAA,gBACpBpB,OAAA,CAACzB,UAAU;cAAA6C,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACnC1B,OAAA,CAACxB,MAAM;cACLkC,KAAK,EAAEM,SAAU;cACjBsB,KAAK,EAAC,YAAY;cAClBC,QAAQ,EAAGC,CAAC,IAAKvB,YAAY,CAACuB,CAAC,CAAClC,MAAM,CAACI,KAAK,CAAE;cAAAU,QAAA,gBAE9CpB,OAAA,CAACvB,QAAQ;gBAACiC,KAAK,EAAC,QAAQ;gBAAAU,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC9C1B,OAAA,CAACvB,QAAQ;gBAACiC,KAAK,EAAC,SAAS;gBAAAU,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClD1B,OAAA,CAACvB,QAAQ;gBAACiC,KAAK,EAAC,SAAS;gBAAAU,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClD1B,OAAA,CAACvB,QAAQ;gBAACiC,KAAK,EAAC,OAAO;gBAAAU,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC5C1B,OAAA,CAACvB,QAAQ;gBAACiC,KAAK,EAAC,QAAQ;gBAAAU,QAAA,EAAC;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACP1B,OAAA,CAAC9B,IAAI;UAAC+D,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eAC9BpB,OAAA,CAAC1B,WAAW;YAAC+D,SAAS;YAAAjB,QAAA,gBACpBpB,OAAA,CAACzB,UAAU;cAAA6C,QAAA,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/B1B,OAAA,CAACxB,MAAM;cACLkC,KAAK,EAAEQ,MAAO;cACdoB,KAAK,EAAC,QAAQ;cACdC,QAAQ,EAAGC,CAAC,IAAKrB,SAAS,CAACqB,CAAC,CAAClC,MAAM,CAACI,KAAK,CAAE;cAAAU,QAAA,gBAE3CpB,OAAA,CAACvB,QAAQ;gBAACiC,KAAK,EAAC,KAAK;gBAAAU,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC5C1B,OAAA,CAACvB,QAAQ;gBAACiC,KAAK,EAAC,OAAO;gBAAAU,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxC1B,OAAA,CAACvB,QAAQ;gBAACiC,KAAK,EAAC,OAAO;gBAAAU,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxC1B,OAAA,CAACvB,QAAQ;gBAACiC,KAAK,EAAC,MAAM;gBAAAU,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtC1B,OAAA,CAACvB,QAAQ;gBAACiC,KAAK,EAAC,MAAM;gBAAAU,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EACNV,SAAS,KAAK,QAAQ,iBACrBhB,OAAA,CAAAE,SAAA;UAAAkB,QAAA,eACEpB,OAAA,CAAC9B,IAAI;YAAC+D,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAhB,QAAA,eAC9BpB,OAAA,CAAC/B,UAAU;cAACoD,OAAO,EAAC,OAAO;cAACV,KAAK,EAAC,eAAe;cAAAS,QAAA,EAAC;YAElD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC,gBACP,CACH,eACD1B,OAAA,CAAC9B,IAAI;UAAC+D,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eAC9BpB,OAAA,CAACtB,MAAM;YAAC2C,OAAO,EAAC,WAAW;YAACgB,SAAS;YAACT,EAAE,EAAE;cAAEa,MAAM,EAAE;YAAG,CAAE;YAAArB,QAAA,EAAC;UAE1D;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR1B,OAAA,CAAC9B,IAAI;MAAC6D,SAAS;MAACC,OAAO,EAAE,CAAE;MAACJ,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAV,QAAA,gBACxCpB,OAAA,CAAC9B,IAAI;QAAC+D,IAAI;QAACC,EAAE,EAAE,EAAG;QAACQ,EAAE,EAAE,CAAE;QAAAtB,QAAA,eACvBpB,OAAA,CAAC7B,KAAK;UAACyD,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAT,QAAA,gBAClBpB,OAAA,CAAC/B,UAAU;YAACoD,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb1B,OAAA,CAACF,mBAAmB;YAAC6C,KAAK,EAAC,MAAM;YAACF,MAAM,EAAE,GAAI;YAAArB,QAAA,eAC5CpB,OAAA,CAACd,QAAQ;cAAC0D,IAAI,EAAEzC,SAAU;cAAAiB,QAAA,gBACxBpB,OAAA,CAACV,aAAa;gBAACuD,eAAe,EAAC;cAAK;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC1B,OAAA,CAACZ,KAAK;gBAAC0D,OAAO,EAAC;cAAO;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzB1B,OAAA,CAACX,KAAK;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACT1B,OAAA,CAACT,OAAO;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX1B,OAAA,CAACR,MAAM;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACV1B,OAAA,CAACb,GAAG;gBAAC2D,OAAO,EAAC,OAAO;gBAACC,IAAI,EAAC,SAAS;gBAACtC,IAAI,EAAC;cAAc;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1D1B,OAAA,CAACb,GAAG;gBAAC2D,OAAO,EAAC,QAAQ;gBAACC,IAAI,EAAC,SAAS;gBAACtC,IAAI,EAAC;cAAQ;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACP1B,OAAA,CAAC9B,IAAI;QAAC+D,IAAI;QAACC,EAAE,EAAE,EAAG;QAACQ,EAAE,EAAE,CAAE;QAAAtB,QAAA,eACvBpB,OAAA,CAAC7B,KAAK;UAACyD,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAT,QAAA,gBAClBpB,OAAA,CAAC/B,UAAU;YAACoD,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb1B,OAAA,CAACF,mBAAmB;YAAC6C,KAAK,EAAC,MAAM;YAACF,MAAM,EAAE,GAAI;YAAArB,QAAA,eAC5CpB,OAAA,CAACL,QAAQ;cAAAyB,QAAA,gBACPpB,OAAA,CAACJ,GAAG;gBACFgD,IAAI,EAAEpC,YAAa;gBACnBwC,EAAE,EAAC,KAAK;gBACRC,EAAE,EAAC,KAAK;gBACRC,SAAS,EAAE,KAAM;gBACjBZ,KAAK,EAAEA,CAAC;kBAAE7B,IAAI;kBAAE0C;gBAAQ,CAAC,KAAK,GAAG1C,IAAI,IAAI,CAAC0C,OAAO,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAI;gBACvEC,WAAW,EAAE,EAAG;gBAChBN,IAAI,EAAC,SAAS;gBACdD,OAAO,EAAC,OAAO;gBAAA1B,QAAA,EAEdZ,YAAY,CAAC8C,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC7BxD,OAAA,CAACH,IAAI;kBAAuBkD,IAAI,EAAEQ,KAAK,CAAC5C;gBAAM,GAAnC,QAAQ6C,KAAK,EAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAsB,CACjD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1B,OAAA,CAACT,OAAO;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP1B,OAAA,CAAC9B,IAAI;MAAC6D,SAAS;MAACC,OAAO,EAAE,CAAE;MAACJ,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAV,QAAA,eACxCpB,OAAA,CAAC9B,IAAI;QAAC+D,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAd,QAAA,eAChBpB,OAAA,CAAC7B,KAAK;UAACyD,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAT,QAAA,gBAClBpB,OAAA,CAAC/B,UAAU;YAACoD,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb1B,OAAA,CAACF,mBAAmB;YAAC6C,KAAK,EAAC,MAAM;YAACF,MAAM,EAAE,GAAI;YAAArB,QAAA,eAC5CpB,OAAA,CAACP,SAAS;cAACmD,IAAI,EAAEzC,SAAU;cAAAiB,QAAA,gBACzBpB,OAAA,CAACV,aAAa;gBAACuD,eAAe,EAAC;cAAK;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC1B,OAAA,CAACZ,KAAK;gBAAC0D,OAAO,EAAC;cAAO;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzB1B,OAAA,CAACX,KAAK;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACT1B,OAAA,CAACT,OAAO;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX1B,OAAA,CAACR,MAAM;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACV1B,OAAA,CAACN,IAAI;gBAAC+D,IAAI,EAAC,UAAU;gBAACX,OAAO,EAAC,QAAQ;gBAACY,MAAM,EAAC,SAAS;gBAACC,WAAW,EAAE,CAAE;gBAAClD,IAAI,EAAC;cAAU;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP1B,OAAA,CAAC7B,KAAK;MAACyD,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAT,QAAA,gBAClBpB,OAAA,CAAC/B,UAAU;QAACoD,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb1B,OAAA,CAAClB,cAAc;QAAAsC,QAAA,eACbpB,OAAA,CAACrB,KAAK;UAAAyC,QAAA,gBACJpB,OAAA,CAACjB,SAAS;YAAAqC,QAAA,eACRpB,OAAA,CAAChB,QAAQ;cAAAoC,QAAA,gBACPpB,OAAA,CAACnB,SAAS;gBAAAuC,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC3B1B,OAAA,CAACnB,SAAS;gBAAC+E,KAAK,EAAC,OAAO;gBAAAxC,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9C1B,OAAA,CAACnB,SAAS;gBAAC+E,KAAK,EAAC,OAAO;gBAAAxC,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/C1B,OAAA,CAACnB,SAAS;gBAAC+E,KAAK,EAAC,OAAO;gBAAAxC,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpD1B,OAAA,CAACnB,SAAS;gBAAC+E,KAAK,EAAC,QAAQ;gBAAAxC,QAAA,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ1B,OAAA,CAACpB,SAAS;YAAAwC,QAAA,EACPR,YAAY,CAAC0C,GAAG,CAAEO,GAAG,iBACpB7D,OAAA,CAAChB,QAAQ;cAAAoC,QAAA,gBACPpB,OAAA,CAACnB,SAAS;gBAACiF,SAAS,EAAC,IAAI;gBAACC,KAAK,EAAC,KAAK;gBAAA3C,QAAA,EAClCyC,GAAG,CAACpD;cAAI;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACZ1B,OAAA,CAACnB,SAAS;gBAAC+E,KAAK,EAAC,OAAO;gBAAAxC,QAAA,EAAEyC,GAAG,CAACxD,KAAK,CAAC2D,cAAc,CAAC;cAAC;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjE1B,OAAA,CAACnB,SAAS;gBAAC+E,KAAK,EAAC,OAAO;gBAAAxC,QAAA,EAAEyC,GAAG,CAACvD,MAAM,CAAC0D,cAAc,CAAC;cAAC;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClE1B,OAAA,CAACnB,SAAS;gBAAC+E,KAAK,EAAC,OAAO;gBAAAxC,QAAA,GAAEyC,GAAG,CAAChD,WAAW,EAAC,GAAC;cAAA;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACvD1B,OAAA,CAACnB,SAAS;gBAAC+E,KAAK,EAAC,QAAQ;gBAAAxC,QAAA,eACvBpB,OAAA,CAACf,IAAI;kBACHqD,KAAK,EAAEuB,GAAG,CAAChD,WAAW,IAAI,GAAG,GAAG,YAAY,GAAG,cAAe;kBAC9DF,KAAK,EAAEkD,GAAG,CAAChD,WAAW,IAAI,GAAG,GAAG,SAAS,GAAG,SAAU;kBACtDoD,IAAI,EAAC;gBAAO;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA,GAbCmC,GAAG,CAACpD,IAAI;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAcb,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEZ;AAACX,EAAA,CAjLQD,YAAY;AAAAoD,EAAA,GAAZpD,YAAY;AAmLrB,eAAeA,YAAY;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}